
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Head from "next/head";
import { toast } from "sonner";
import { Upload } from "lucide-react";
import TicketFormSkeleton from "@/components/TicketFormSkeleton";
import { submitSupportTicket } from "@/services/supportTicket.service";
import { getGrants } from "@/services/supportTicket.service";
import { getProfile, transformProfileData } from "@/services/profile-service";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface TicketFormData {
  category: string;
  priority: string;
  title: string;
  description: string;
  email: string;
  phone: string;
  status: string;
  grant?: number;
  point_of_contact_name: string;
}

interface Grant {
  id: number;
  grant_name: string;
  annual_budget: number;
  grant_purpose?: string;
}

export default function CreateSupportTicketPage() {
  const router = useRouter();

  const [formData, setFormData] = useState<TicketFormData>({
    category: "",
    priority: "",
    title: "",
    description: "",
    email: "",
    phone: "",
    status: "open",
    grant: undefined,
    point_of_contact_name: "",
  });

  const [file, setFile] = useState<File | null>(null);
  const [grants, setGrants] = useState<Grant[]>([]);
  const [serverErrors, setServerErrors] = useState<Record<string, string[]>>({});
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      try {
        const grantsResult = await getGrants();
        setGrants(grantsResult || []);

        const profileResponse = await getProfile();
        if (profileResponse.status === "SUCCESS") {
          const profileData = transformProfileData(profileResponse.data);
          setFormData((prev) => ({
            ...prev,
            email: profileData.contact.email || "",
            phone: profileData.contact.phone || "",
            point_of_contact_name: profileData.contact.fullName || "",
          }));
        } else {
          toast.error("Failed to load profile data.");
        }
      } catch (error) {
        toast.error("Failed to load data.");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "grant" ? Number(value) : value,
    }));
    if (serverErrors[name]) {
      setServerErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFile(e.target.files?.[0] || null);
    if (serverErrors.file) {
      setServerErrors((prev) => ({ ...prev, file: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setServerErrors({});

    setSubmitting(true);
    try {
      await submitSupportTicket(formData, file);
      toast.success("Ticket submitted successfully!");
      router.push("/support-ticket/my-tickets");
      router.replace("/support-ticket/my-tickets");
    } catch (error: any) {
      const errors = error.response?.data?.errors;
      if (error.response?.status === 400 && errors) {
        setServerErrors(errors);
        toast.error("Please fix the highlighted errors.");
      } else {
        toast.error(error.response?.data?.detail || "Submission failed.");
      }
    } finally {
      setSubmitting(false);
    }
  };

  const renderError = (field: string) =>
    serverErrors[field] && (
      <p className="text-xs text-red-600 font-medium mt-1">{serverErrors[field][0]}</p>
    );

  return (
    <>
      <Head>
        <title>Create Support Ticket - Grant Management System</title>
      </Head>

      <div className="w-full px-4 py-6 min-h-screen">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-white shadow-xl border border-gray-200 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-teal-50 to-teal-100 border-b border-teal-200 px-8 py-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-teal-600 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-teal-800">
                    Support Request Form
                  </CardTitle>
                  <CardDescription className="text-teal-700 text-base mt-1">
                    Please provide detailed information about your issue
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          <CardContent className="p-8">
            {loading || submitting ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-gray-600 font-medium">
                    {loading ? "Loading form..." : "Submitting ticket..."}
                  </p>
                </div>
              </div>
            ) : (
              <>
                {serverErrors.non_field_errors && (
                  <div className="mb-8 p-4 border border-red-200 bg-red-50 text-sm text-red-600 rounded-xl shadow-sm">
                    <div className="flex items-center gap-2 mb-2">
                      <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <span className="font-semibold">Please fix the following errors:</span>
                    </div>
                    {serverErrors.non_field_errors.map((err, i) => (
                      <p key={i} className="font-medium ml-7">{err}</p>
                    ))}
                  </div>
                )}

                <form onSubmit={handleSubmit} encType="multipart/form-data" className="space-y-8">
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-900 mb-3">
                      <span className="flex items-center gap-2">
                        <svg className="w-4 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        Grant Selection
                      </span>
                    </label>
                    <select
                      name="grant"
                      value={formData.grant || ""}
                      onChange={handleChange}
                      className="w-full px-4 py-3 text-sm border border-gray-300 rounded-xl focus:border-gray-500 focus:ring-2 focus:ring-gray-200 outline-none transition-all bg-white text-gray-900 hover:border-gray-400"
                    >
                      <option value="">Choose a grant...</option>
                      {grants.map((grant) => (
                        <option key={grant.id} value={grant.id}>
                          {grant.grant_name} — ₹{Number(grant.annual_budget).toLocaleString("en-IN")}{" "}
                          {grant.grant_purpose ? `(${grant.grant_purpose.slice(0, 50)}...)` : ""}
                        </option>
                      ))}
                    </select>
                    {renderError("grant")}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-900 mb-3">
                        <span className="flex items-center gap-2">
                          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                          </svg>
                          Category
                        </span>
                      </label>
                      <select
                        name="category"
                        value={formData.category}
                        onChange={handleChange}
                        className="w-full px-4 py-3 text-sm border border-gray-300 rounded-xl focus:border-gray-500 focus:ring-2 focus:ring-gray-200 outline-none transition-all bg-white text-gray-900 hover:border-gray-400"
                      >
                        <option value="">Choose category...</option>
                        <option value="financial">💰 Financial</option>
                        <option value="reporting">📊 Reporting</option>
                        <option value="documents">📄 Documents</option>
                        <option value="technical support">🔧 Technical Support</option>
                        <option value="applications">📱 Applications</option>
                        <option value="account management">👤 Account Management</option>
                      </select>
                      {renderError("category")}
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-900 mb-3">
                        <span className="flex items-center gap-2">
                          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                          Priority Level
                        </span>
                      </label>
                      <select
                        name="priority"
                        value={formData.priority}
                        onChange={handleChange}
                        className="w-full px-4 py-3 text-sm border border-gray-300 rounded-xl focus:border-gray-500 focus:ring-2 focus:ring-gray-200 outline-none transition-all bg-white text-gray-900 hover:border-gray-400"
                      >
                        <option value="">Choose priority...</option>
                        <option value="low">🟢 Low - General inquiry</option>
                        <option value="medium">🟡 Medium - Standard issue</option>
                        <option value="high">🟠 High - Important matter</option>
                        <option value="urgent">🔴 Urgent - Critical issue</option>
                      </select>
                      {renderError("priority")}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-900 mb-3">
                      <span className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                        </svg>
                        Issue Title
                      </span>
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleChange}
                      placeholder="Brief summary of your issue..."
                      className="w-full px-4 py-3 text-sm border border-gray-300 rounded-xl focus:border-gray-500 focus:ring-2 focus:ring-gray-200 outline-none transition-all bg-white text-gray-900 hover:border-gray-400 placeholder:text-gray-500"
                    />
                    {renderError("title")}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-900 mb-3">
                      <span className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Detailed Description
                      </span>
                    </label>
                    <textarea
                      name="description"
                      rows={6}
                      value={formData.description}
                      onChange={handleChange}
                      placeholder="Please provide a detailed description of your issue, including any error messages, steps to reproduce, and what you expected to happen..."
                      className="w-full px-4 py-3 text-sm border border-gray-300 rounded-xl focus:border-gray-500 focus:ring-2 focus:ring-gray-200 outline-none transition-all bg-white text-gray-900 hover:border-gray-400 placeholder:text-gray-500 resize-none"
                    />
                    <p className="text-xs text-gray-500 mt-1">The more details you provide, the faster we can help you resolve the issue.</p>
                    {renderError("description")}
                  </div>

                  <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Contact Information
                    </h3>

                    <div className="space-y-6">
                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-900">
                          Point of Contact Name
                        </label>
                        <input
                          type="text"
                          name="point_of_contact_name"
                          value={formData.point_of_contact_name}
                          onChange={handleChange}
                          placeholder="Your full name"
                          className="w-full px-4 py-3 text-sm border border-gray-300 rounded-xl focus:border-gray-500 focus:ring-2 focus:ring-gray-200 outline-none transition-all bg-white text-gray-900 hover:border-gray-400 placeholder:text-gray-500"
                        />
                        {renderError("point_of_contact_name")}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <label className="block text-sm font-semibold text-gray-900">
                            Email Address
                          </label>
                          <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            placeholder="<EMAIL>"
                            className="w-full px-4 py-3 text-sm border border-gray-300 rounded-xl focus:border-gray-500 focus:ring-2 focus:ring-gray-200 outline-none transition-all bg-white text-gray-900 hover:border-gray-400 placeholder:text-gray-500"
                          />
                          {renderError("email")}
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-semibold text-gray-900">
                            Phone Number
                          </label>
                          <input
                            type="text"
                            name="phone"
                            value={formData.phone}
                            onChange={handleChange}
                            placeholder="+91 98765 43210"
                            className="w-full px-4 py-3 text-sm border border-gray-300 rounded-xl focus:border-gray-500 focus:ring-2 focus:ring-gray-200 outline-none transition-all bg-white text-gray-900 hover:border-gray-400 placeholder:text-gray-500"
                          />
                          {renderError("phone")}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-900 mb-3">
                      <span className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                        Attachment (Optional)
                      </span>
                    </label>
                    <label
                      htmlFor="file-upload"
                      className="flex flex-col items-center justify-center gap-4 px-6 py-8 bg-gray-50 border-2 border-dashed border-gray-300 rounded-xl cursor-pointer hover:bg-gray-100 hover:border-gray-400 transition-all group"
                    >
                      <div className="p-3 bg-gray-200 rounded-full group-hover:bg-gray-300 transition-colors">
                        <Upload className="w-6 h-6 text-gray-600" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-semibold text-gray-900 mb-1">
                          {file ? "Change file" : "Upload supporting document"}
                        </p>
                        <p className="text-xs text-gray-500">
                          PDF, PNG, JPG, DOC files up to 10MB
                        </p>
                      </div>
                      <input
                        id="file-upload"
                        name="file"
                        type="file"
                        accept=".pdf,.png,.jpg,.jpeg,.doc,.docx"
                        onChange={handleFileChange}
                        className="hidden"
                      />
                    </label>
                    {file && (
                      <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center gap-2">
                          <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          <span className="text-sm font-medium text-green-800">
                            File selected: {file.name}
                          </span>
                        </div>
                      </div>
                    )}
                    {renderError("file")}
                  </div>

                  <div className="flex flex-col sm:flex-row justify-end gap-4 pt-8 border-t border-gray-200">
                    <Button
                      type="button"
                      onClick={() => router.back()}
                      className="px-8 py-3 text-sm font-semibold bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 rounded-xl shadow-sm hover:shadow-md order-2 sm:order-1"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={submitting}
                      className="px-8 py-3 text-sm font-semibold bg-teal-600 text-white hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all duration-200 rounded-xl shadow-lg hover:shadow-xl disabled:shadow-sm order-1 sm:order-2 flex items-center justify-center gap-2"
                    >
                      {submitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Submitting...
                        </>
                      ) : (
                        <>
                          <Upload className="w-4 h-4" />
                          Submit Ticket
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </>
            )}
          </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
