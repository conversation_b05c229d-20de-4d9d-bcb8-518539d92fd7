"use client";

import Layout from "@/components/grantmaker/Layout";
import { PlusIcon, Undo2Icon } from "lucide-react";
import { Button, buttonVariants } from "@/components/ui/button";
import Link from "next/link";
import { notFound, useParams, useRouter } from "next/navigation";
import { impact, outcomes } from "@/data/quantitative";
import { ImpactHeader } from "@/components/reports/quantitative/Impacts";
import { OutcomesList } from "@/components/reports/quantitative/Outcomes";
import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import {
  getGrantmakerImpact,
  getGrantmakerOutcomesForImpact,
} from "@/services/grantmaker/quantitative-service";
import {
  ImpactType,
  OutcomeType,
  OutcomeTypeWithImpact,
} from "@/types/quantitative";
import { Skeleton } from "@/components/ui/skeleton";

export default function QuantitativeReportImpactPage() {
  const params = useParams();
  const id: number = Number(params.impact_id);
  const router = useRouter();

  const impactQuery = useQuery<ImpactType | undefined, Error>({
    queryKey: ["impacts", id],
    queryFn: async () => {
      const impacts = await getGrantmakerImpact(id.toString());
      return impacts;
    },
  });

  const outcomesQuery = useQuery<OutcomeTypeWithImpact[], Error>({
    queryKey: ["outcomes", id],
    queryFn: async () => {
      const outcomes = await getGrantmakerOutcomesForImpact(id.toString());
      return outcomes;
    },
  });

  if (!impactQuery.isLoading && !impactQuery.data) {
    notFound();
  }

  return (
    <Layout title="Quantitative Reports">
      <motion.div
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: -10, opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Button variant="ghost" onClick={() => router.back()} className="mb-2">
          <Undo2Icon />
          Back
        </Button>
      </motion.div>

      {impactQuery.isLoading && <Skeleton className="h-[200px]" />}
      {impactQuery.data && (
        <ImpactHeader
          impact={impactQuery.data}
          outcomes={outcomesQuery.data ?? []}
        />
      )}

      <div className="space-y-4 pt-8">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Outcomes ({outcomesQuery.data?.length ?? 0})
          </h2>

          <Link
            href={`/grantmaker/reports/quantitative/${id}/outcome/new`}
            className={buttonVariants()}
          >
            <PlusIcon />
            New Outcome
          </Link>
        </div>

        {outcomesQuery.data && (
          <OutcomesList
            outcomes={outcomesQuery.data}
            userType={"GRANT_MAKER"}
          />
        )}
      </div>
    </Layout>
  );
}
