import { NewOutcomeType } from "@/app/milestones-reports/_components/quantitative/edit-outcome";
import { UserType } from "@/contexts/AuthContext";
import apiClient from "@/lib/apiClient";
import {
  ImpactSchema,
  OutcomeSchema,
  OutcomeSchemaWithImpact,
  OutcomeType,
  OutcomeTypeWithImpact,
} from "@/types/quantitative";
import z from "zod";

export const GrantSchema = z.object({
  id: z.string(),
  grant_name: z.string(),
  annual_budget: z.number().optional(),
});

export type GrantType = z.infer<typeof GrantSchema>;

export async function getGrantmakerImpacts(grantee_id: string) {
  const params = new URLSearchParams();
  params.set("organization_id", grantee_id);

  const response = await apiClient.get(
    `/api/reports/grantmaker/quantitative/impacts?${params.toString()}`,
  );

  const parsedImpacts = z.array(ImpactSchema).safeParse(response.data);

  if (!parsedImpacts.success) return [];

  return parsedImpacts.data;
}

export async function getGranteeImpacts() {
  const response = await apiClient.get(`/api/reports/quantitative/impacts`);

  const parsedImpacts = z.array(ImpactSchema).safeParse(response.data);

  if (!parsedImpacts.success) return [];

  return parsedImpacts.data;
}

export async function getGrantmakerImpact(impact_id: string) {
  const response = await apiClient.get(
    `/api/reports/grantmaker/quantitative/impacts/${impact_id}`,
  );

  const parsedImpacts = ImpactSchema.safeParse(response.data);

  if (!parsedImpacts.success) return undefined;

  return parsedImpacts.data;
}

export async function getGranteeImpact(impact_id: string) {
  const response = await apiClient.get(
    `/api/reports/quantitative/impacts/${impact_id}`,
  );

  const parsedImpacts = ImpactSchema.safeParse(response.data);

  if (!parsedImpacts.success) return undefined;

  return parsedImpacts.data;
}

export async function getGrantmakerOutcomesForImpact(impact_id: string) {
  const params = new URLSearchParams();
  params.set("impact_id", impact_id);

  const response = await apiClient.get(
    `/api/reports/grantmaker/quantitative/outcomes/?${params.toString()}`,
  );

  const parsedImpacts = z
    .array(OutcomeSchemaWithImpact)
    .safeParse(response.data);

  console.log(parsedImpacts);

  if (!parsedImpacts.success) return [];

  return parsedImpacts.data;
}

export async function getGranteeOutcomesForImpact(impact_id: string) {
  const params = new URLSearchParams();
  params.set("impact_id", impact_id);

  const response = await apiClient.get(
    `/api/reports/quantitative/outcomes/?${params.toString()}`,
  );

  const parsedImpacts = z
    .array(OutcomeSchemaWithImpact)
    .safeParse(response.data);

  console.log(parsedImpacts);

  if (!parsedImpacts.success) return [];

  return parsedImpacts.data;
}

export async function getGrantmakerOutcome(outcome_id: string) {
  const response = await apiClient.get(
    `/api/reports/grantmaker/quantitative/outcomes/${outcome_id}`,
  );

  const parsedOutcomes = OutcomeSchemaWithImpact.safeParse(response.data);

  if (!parsedOutcomes.success) return undefined;

  return parsedOutcomes.data;
}

export async function getGranteeOutcome(outcome_id: string) {
  const response = await apiClient.get(
    `/api/reports/quantitative/outcomes/${outcome_id}`,
  );

  const parsedOutcomes = OutcomeSchemaWithImpact.safeParse(response.data);

  if (!parsedOutcomes.success) return undefined;

  return parsedOutcomes.data;
}
